{%- render 'section-furniture-showcase' -%}

{% schema %}
{
  "name": "Furniture Showcase",
  "class": "index-section",
  "settings": [
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Discover our curated collections and find the perfect pieces to elevate your space"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "Heading Size",
      "default": "h2",
      "options": [
        {
          "value": "h3",
          "label": "Small"
        },
        {
          "value": "h2",
          "label": "Medium"
        },
        {
          "value": "h1",
          "label": "Large"
        },
        {
          "value": "h0",
          "label": "Extra Large"
        }
      ]
    },
    {
      "type": "header",
      "content": "Navigation Menu"
    },
    {
      "type": "text",
      "id": "nav_item_1",
      "label": "Navigation Item 1",
      "default": "Savanna"
    },
    {
      "type": "url",
      "id": "nav_link_1",
      "label": "Navigation Link 1"
    },
    {
      "type": "text",
      "id": "nav_item_2",
      "label": "Navigation Item 2",
      "default": "Cas"
    },
    {
      "type": "url",
      "id": "nav_link_2",
      "label": "Navigation Link 2"
    },
    {
      "type": "text",
      "id": "nav_item_3",
      "label": "Navigation Item 3",
      "default": "Helio"
    },
    {
      "type": "url",
      "id": "nav_link_3",
      "label": "Navigation Link 3"
    },
    {
      "type": "text",
      "id": "nav_item_4",
      "label": "Navigation Item 4",
      "default": "Terra"
    },
    {
      "type": "url",
      "id": "nav_link_4",
      "label": "Navigation Link 4"
    },
    {
      "type": "text",
      "id": "nav_item_5",
      "label": "Navigation Item 5",
      "default": "Stria"
    },
    {
      "type": "url",
      "id": "nav_link_5",
      "label": "Navigation Link 5"
    },
    {
      "type": "text",
      "id": "nav_item_6",
      "label": "Navigation Item 6",
      "default": "Opus"
    },
    {
      "type": "url",
      "id": "nav_link_6",
      "label": "Navigation Link 6"
    },
    {
      "type": "header",
      "content": "Slide 1 Media (Images & Videos)"
    },
    {
      "type": "paragraph",
      "content": "Supported formats: WebP (recommended), JPEG (≥80% quality), PNG (transparency), SVG (vector). Videos: MP4 (H.264), WebM (VP9) - auto-play muted & loop only."
    },
    {
      "type": "image_picker",
      "id": "image_1",
      "label": "Slide 1 - Image 1"
    },
    {
      "type": "video",
      "id": "video_1",
      "label": "Slide 1 - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "video_url_1",
      "label": "Slide 1 - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "Slide 1 - Image 2"
    },
    {
      "type": "video",
      "id": "video_2",
      "label": "Slide 1 - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "video_url_2",
      "label": "Slide 1 - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "image_3",
      "label": "Slide 1 - Image 3"
    },
    {
      "type": "video",
      "id": "video_3",
      "label": "Slide 1 - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "video_url_3",
      "label": "Slide 1 - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "image_4",
      "label": "Slide 1 - Image 4"
    },
    {
      "type": "video",
      "id": "video_4",
      "label": "Slide 1 - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "video_url_4",
      "label": "Slide 1 - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "image_5",
      "label": "Slide 1 - Image 5"
    },
    {
      "type": "video",
      "id": "video_5",
      "label": "Slide 1 - Video 5 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "video_url_5",
      "label": "Slide 1 - Video URL 5 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 1 Mobile Media (Images & Videos)"
    },
    {
      "type": "paragraph",
      "content": "Mobile-specific media that will be displayed instead of PC media on mobile devices."
    },
    {
      "type": "image_picker",
      "id": "mobile_image_1",
      "label": "Slide 1 Mobile - Image 1"
    },
    {
      "type": "video",
      "id": "mobile_video_1",
      "label": "Slide 1 Mobile - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "mobile_video_url_1",
      "label": "Slide 1 Mobile - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "mobile_image_2",
      "label": "Slide 1 Mobile - Image 2"
    },
    {
      "type": "video",
      "id": "mobile_video_2",
      "label": "Slide 1 Mobile - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "mobile_video_url_2",
      "label": "Slide 1 Mobile - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "mobile_image_3",
      "label": "Slide 1 Mobile - Image 3"
    },
    {
      "type": "video",
      "id": "mobile_video_3",
      "label": "Slide 1 Mobile - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "mobile_video_url_3",
      "label": "Slide 1 Mobile - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "mobile_image_4",
      "label": "Slide 1 Mobile - Image 4"
    },
    {
      "type": "video",
      "id": "mobile_video_4",
      "label": "Slide 1 Mobile - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "mobile_video_url_4",
      "label": "Slide 1 Mobile - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 2 Media (Images & Videos)"
    },
    {
      "type": "image_picker",
      "id": "slide2_image_1",
      "label": "Slide 2 - Image 1"
    },
    {
      "type": "video",
      "id": "slide2_video_1",
      "label": "Slide 2 - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide2_video_url_1",
      "label": "Slide 2 - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide2_image_2",
      "label": "Slide 2 - Image 2"
    },
    {
      "type": "video",
      "id": "slide2_video_2",
      "label": "Slide 2 - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide2_video_url_2",
      "label": "Slide 2 - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide2_image_3",
      "label": "Slide 2 - Image 3"
    },
    {
      "type": "video",
      "id": "slide2_video_3",
      "label": "Slide 2 - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide2_video_url_3",
      "label": "Slide 2 - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide2_image_4",
      "label": "Slide 2 - Image 4"
    },
    {
      "type": "video",
      "id": "slide2_video_4",
      "label": "Slide 2 - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide2_video_url_4",
      "label": "Slide 2 - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide2_image_5",
      "label": "Slide 2 - Image 5"
    },
    {
      "type": "video",
      "id": "slide2_video_5",
      "label": "Slide 2 - Video 5 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide2_video_url_5",
      "label": "Slide 2 - Video URL 5 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 2 Mobile Media (Images & Videos)"
    },
    {
      "type": "paragraph",
      "content": "Mobile-specific media that will be displayed instead of PC media on mobile devices."
    },
    {
      "type": "image_picker",
      "id": "slide2_mobile_image_1",
      "label": "Slide 2 Mobile - Image 1"
    },
    {
      "type": "video",
      "id": "slide2_mobile_video_1",
      "label": "Slide 2 Mobile - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide2_mobile_video_url_1",
      "label": "Slide 2 Mobile - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide2_mobile_image_2",
      "label": "Slide 2 Mobile - Image 2"
    },
    {
      "type": "video",
      "id": "slide2_mobile_video_2",
      "label": "Slide 2 Mobile - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide2_mobile_video_url_2",
      "label": "Slide 2 Mobile - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide2_mobile_image_3",
      "label": "Slide 2 Mobile - Image 3"
    },
    {
      "type": "video",
      "id": "slide2_mobile_video_3",
      "label": "Slide 2 Mobile - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide2_mobile_video_url_3",
      "label": "Slide 2 Mobile - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide2_mobile_image_4",
      "label": "Slide 2 Mobile - Image 4"
    },
    {
      "type": "video",
      "id": "slide2_mobile_video_4",
      "label": "Slide 2 Mobile - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide2_mobile_video_url_4",
      "label": "Slide 2 Mobile - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 3 Media (Images & Videos)"
    },
    {
      "type": "image_picker",
      "id": "slide3_image_1",
      "label": "Slide 3 - Image 1"
    },
    {
      "type": "video",
      "id": "slide3_video_1",
      "label": "Slide 3 - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide3_video_url_1",
      "label": "Slide 3 - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide3_image_2",
      "label": "Slide 3 - Image 2"
    },
    {
      "type": "video",
      "id": "slide3_video_2",
      "label": "Slide 3 - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide3_video_url_2",
      "label": "Slide 3 - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide3_image_3",
      "label": "Slide 3 - Image 3"
    },
    {
      "type": "video",
      "id": "slide3_video_3",
      "label": "Slide 3 - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide3_video_url_3",
      "label": "Slide 3 - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide3_image_4",
      "label": "Slide 3 - Image 4"
    },
    {
      "type": "video",
      "id": "slide3_video_4",
      "label": "Slide 3 - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide3_video_url_4",
      "label": "Slide 3 - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide3_image_5",
      "label": "Slide 3 - Image 5"
    },
    {
      "type": "video",
      "id": "slide3_video_5",
      "label": "Slide 3 - Video 5 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide3_video_url_5",
      "label": "Slide 3 - Video URL 5 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 3 Mobile Media (Images & Videos)"
    },
    {
      "type": "paragraph",
      "content": "Mobile-specific media that will be displayed instead of PC media on mobile devices."
    },
    {
      "type": "image_picker",
      "id": "slide3_mobile_image_1",
      "label": "Slide 3 Mobile - Image 1"
    },
    {
      "type": "video",
      "id": "slide3_mobile_video_1",
      "label": "Slide 3 Mobile - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide3_mobile_video_url_1",
      "label": "Slide 3 Mobile - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide3_mobile_image_2",
      "label": "Slide 3 Mobile - Image 2"
    },
    {
      "type": "video",
      "id": "slide3_mobile_video_2",
      "label": "Slide 3 Mobile - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide3_mobile_video_url_2",
      "label": "Slide 3 Mobile - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide3_mobile_image_3",
      "label": "Slide 3 Mobile - Image 3"
    },
    {
      "type": "video",
      "id": "slide3_mobile_video_3",
      "label": "Slide 3 Mobile - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide3_mobile_video_url_3",
      "label": "Slide 3 Mobile - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide3_mobile_image_4",
      "label": "Slide 3 Mobile - Image 4"
    },
    {
      "type": "video",
      "id": "slide3_mobile_video_4",
      "label": "Slide 3 Mobile - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide3_mobile_video_url_4",
      "label": "Slide 3 Mobile - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 4 Media (Images & Videos)"
    },
    {
      "type": "image_picker",
      "id": "slide4_image_1",
      "label": "Slide 4 - Image 1"
    },
    {
      "type": "video",
      "id": "slide4_video_1",
      "label": "Slide 4 - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide4_video_url_1",
      "label": "Slide 4 - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide4_image_2",
      "label": "Slide 4 - Image 2"
    },
    {
      "type": "video",
      "id": "slide4_video_2",
      "label": "Slide 4 - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide4_video_url_2",
      "label": "Slide 4 - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide4_image_3",
      "label": "Slide 4 - Image 3"
    },
    {
      "type": "video",
      "id": "slide4_video_3",
      "label": "Slide 4 - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide4_video_url_3",
      "label": "Slide 4 - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide4_image_4",
      "label": "Slide 4 - Image 4"
    },
    {
      "type": "video",
      "id": "slide4_video_4",
      "label": "Slide 4 - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide4_video_url_4",
      "label": "Slide 4 - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide4_image_5",
      "label": "Slide 4 - Image 5"
    },
    {
      "type": "video",
      "id": "slide4_video_5",
      "label": "Slide 4 - Video 5 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide4_video_url_5",
      "label": "Slide 4 - Video URL 5 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 4 Mobile Media (Images & Videos)"
    },
    {
      "type": "paragraph",
      "content": "Mobile-specific media that will be displayed instead of PC media on mobile devices."
    },
    {
      "type": "image_picker",
      "id": "slide4_mobile_image_1",
      "label": "Slide 4 Mobile - Image 1"
    },
    {
      "type": "video",
      "id": "slide4_mobile_video_1",
      "label": "Slide 4 Mobile - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide4_mobile_video_url_1",
      "label": "Slide 4 Mobile - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide4_mobile_image_2",
      "label": "Slide 4 Mobile - Image 2"
    },
    {
      "type": "video",
      "id": "slide4_mobile_video_2",
      "label": "Slide 4 Mobile - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide4_mobile_video_url_2",
      "label": "Slide 4 Mobile - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide4_mobile_image_3",
      "label": "Slide 4 Mobile - Image 3"
    },
    {
      "type": "video",
      "id": "slide4_mobile_video_3",
      "label": "Slide 4 Mobile - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide4_mobile_video_url_3",
      "label": "Slide 4 Mobile - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide4_mobile_image_4",
      "label": "Slide 4 Mobile - Image 4"
    },
    {
      "type": "video",
      "id": "slide4_mobile_video_4",
      "label": "Slide 4 Mobile - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide4_mobile_video_url_4",
      "label": "Slide 4 Mobile - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 5 Media (Images & Videos)"
    },
    {
      "type": "image_picker",
      "id": "slide5_image_1",
      "label": "Slide 5 - Image 1"
    },
    {
      "type": "video",
      "id": "slide5_video_1",
      "label": "Slide 5 - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide5_video_url_1",
      "label": "Slide 5 - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide5_image_2",
      "label": "Slide 5 - Image 2"
    },
    {
      "type": "video",
      "id": "slide5_video_2",
      "label": "Slide 5 - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide5_video_url_2",
      "label": "Slide 5 - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide5_image_3",
      "label": "Slide 5 - Image 3"
    },
    {
      "type": "video",
      "id": "slide5_video_3",
      "label": "Slide 5 - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide5_video_url_3",
      "label": "Slide 5 - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide5_image_4",
      "label": "Slide 5 - Image 4"
    },
    {
      "type": "video",
      "id": "slide5_video_4",
      "label": "Slide 5 - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide5_video_url_4",
      "label": "Slide 5 - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide5_image_5",
      "label": "Slide 5 - Image 5"
    },
    {
      "type": "video",
      "id": "slide5_video_5",
      "label": "Slide 5 - Video 5 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide5_video_url_5",
      "label": "Slide 5 - Video URL 5 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 5 Mobile Media (Images & Videos)"
    },
    {
      "type": "paragraph",
      "content": "Mobile-specific media that will be displayed instead of PC media on mobile devices."
    },
    {
      "type": "image_picker",
      "id": "slide5_mobile_image_1",
      "label": "Slide 5 Mobile - Image 1"
    },
    {
      "type": "video",
      "id": "slide5_mobile_video_1",
      "label": "Slide 5 Mobile - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide5_mobile_video_url_1",
      "label": "Slide 5 Mobile - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide5_mobile_image_2",
      "label": "Slide 5 Mobile - Image 2"
    },
    {
      "type": "video",
      "id": "slide5_mobile_video_2",
      "label": "Slide 5 Mobile - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide5_mobile_video_url_2",
      "label": "Slide 5 Mobile - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide5_mobile_image_3",
      "label": "Slide 5 Mobile - Image 3"
    },
    {
      "type": "video",
      "id": "slide5_mobile_video_3",
      "label": "Slide 5 Mobile - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide5_mobile_video_url_3",
      "label": "Slide 5 Mobile - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide5_mobile_image_4",
      "label": "Slide 5 Mobile - Image 4"
    },
    {
      "type": "video",
      "id": "slide5_mobile_video_4",
      "label": "Slide 5 Mobile - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide5_mobile_video_url_4",
      "label": "Slide 5 Mobile - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 6 Media (Images & Videos)"
    },
    {
      "type": "image_picker",
      "id": "slide6_image_1",
      "label": "Slide 6 - Image 1"
    },
    {
      "type": "video",
      "id": "slide6_video_1",
      "label": "Slide 6 - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide6_video_url_1",
      "label": "Slide 6 - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide6_image_2",
      "label": "Slide 6 - Image 2"
    },
    {
      "type": "video",
      "id": "slide6_video_2",
      "label": "Slide 6 - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide6_video_url_2",
      "label": "Slide 6 - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide6_image_3",
      "label": "Slide 6 - Image 3"
    },
    {
      "type": "video",
      "id": "slide6_video_3",
      "label": "Slide 6 - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide6_video_url_3",
      "label": "Slide 6 - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide6_image_4",
      "label": "Slide 6 - Image 4"
    },
    {
      "type": "video",
      "id": "slide6_video_4",
      "label": "Slide 6 - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide6_video_url_4",
      "label": "Slide 6 - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide6_image_5",
      "label": "Slide 6 - Image 5"
    },
    {
      "type": "video",
      "id": "slide6_video_5",
      "label": "Slide 6 - Video 5 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide6_video_url_5",
      "label": "Slide 6 - Video URL 5 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Slide 6 Mobile Media (Images & Videos)"
    },
    {
      "type": "paragraph",
      "content": "Mobile-specific media that will be displayed instead of PC media on mobile devices."
    },
    {
      "type": "image_picker",
      "id": "slide6_mobile_image_1",
      "label": "Slide 6 Mobile - Image 1"
    },
    {
      "type": "video",
      "id": "slide6_mobile_video_1",
      "label": "Slide 6 Mobile - Video 1 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide6_mobile_video_url_1",
      "label": "Slide 6 Mobile - Video URL 1 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide6_mobile_image_2",
      "label": "Slide 6 Mobile - Image 2"
    },
    {
      "type": "video",
      "id": "slide6_mobile_video_2",
      "label": "Slide 6 Mobile - Video 2 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide6_mobile_video_url_2",
      "label": "Slide 6 Mobile - Video URL 2 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide6_mobile_image_3",
      "label": "Slide 6 Mobile - Image 3"
    },
    {
      "type": "video",
      "id": "slide6_mobile_video_3",
      "label": "Slide 6 Mobile - Video 3 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide6_mobile_video_url_3",
      "label": "Slide 6 Mobile - Video URL 3 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "image_picker",
      "id": "slide6_mobile_image_4",
      "label": "Slide 6 Mobile - Image 4"
    },
    {
      "type": "video",
      "id": "slide6_mobile_video_4",
      "label": "Slide 6 Mobile - Video 4 (MP4/WebM)",
      "info": "Overrides image if both are set. Auto-plays muted with loop."
    },
    {
      "type": "video_url",
      "id": "slide6_mobile_video_url_4",
      "label": "Slide 6 Mobile - Video URL 4 (YouTube/Vimeo)",
      "accept": ["youtube", "vimeo"],
      "info": "External video - overrides image and video file if set."
    },
    {
      "type": "header",
      "content": "Call to Action - Read More Buttons"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button Label",
      "default": "Read More"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button Style",
      "default": "primary",
      "options": [
        {
          "value": "primary",
          "label": "Primary"
        },
        {
          "value": "secondary",
          "label": "Secondary"
        }
      ]
    },
    {
      "type": "url",
      "id": "button_link_1",
      "label": "Slide 1 - Read More Link"
    },
    {
      "type": "url",
      "id": "button_link_2",
      "label": "Slide 2 - Read More Link"
    },
    {
      "type": "url",
      "id": "button_link_3",
      "label": "Slide 3 - Read More Link"
    },
    {
      "type": "url",
      "id": "button_link_4",
      "label": "Slide 4 - Read More Link"
    },
    {
      "type": "url",
      "id": "button_link_5",
      "label": "Slide 5 - Read More Link"
    },
    {
      "type": "url",
      "id": "button_link_6",
      "label": "Slide 6 - Read More Link"
    },
    {
      "type": "header",
      "content": "Auto-play Settings"
    },
    {
      "type": "checkbox",
      "id": "auto_play",
      "label": "Enable Auto-play",
      "default": true,
      "info": "Automatically cycle through slides"
    },
    {
      "type": "range",
      "id": "auto_play_speed",
      "label": "Auto-play Speed (seconds)",
      "min": 3,
      "max": 10,
      "step": 1,
      "default": 5,
      "info": "Time between slide transitions"
    },
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#f8f8f8"
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "Show Section Divider",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "top_padding",
      "label": "Show Top Padding",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "bottom_padding",
      "label": "Show Bottom Padding",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Furniture Showcase"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header"]
  }
}
{% endschema %}
